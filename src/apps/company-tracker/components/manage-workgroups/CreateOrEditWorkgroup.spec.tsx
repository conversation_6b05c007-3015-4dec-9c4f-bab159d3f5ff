import { vi, Mock } from 'vitest';
import { render, screen } from '@testing-library/react';
import { CreateOrEditWorkgroup } from './CreateOrEditWorkgroup';
import { useGetWorkgroupQuery, useGetWorkgroupsQuery } from '@api/workgroups';
import userEvent from '@testing-library/user-event';
import { ManageWorkgroupTab } from './utils';

vi.mock('react-router-dom', () => ({
  useHistory: () => ({
    push: vi.fn(),
  }),
  withRouter: (component: any) => component,
  generatePath: (path: string) => path,
}));

vi.mock('@api/workgroups', () => ({
  useGetWorkgroupQuery: vi.fn(),
  useGetWorkgroupsQuery: vi.fn(),
}));

vi.mock('./Configuration', () => ({
  __esModule: true,
  Configuration: () => <div data-testid='configuration' />,
}));

vi.mock('./ManageUsers', () => ({
  __esModule: true,
  ManageUsers: () => <div data-testid='manage-users' />,
}));

describe('CreateOrEditWorkgroup component', () => {
  const initiativeId = 'initiative-id';
  const workgroupId = 'workgroup-id';

  beforeEach(() => {
    (useGetWorkgroupQuery as Mock).mockReturnValue({
      data: undefined,
      isLoading: false,
      isError: false,
    });
    (useGetWorkgroupsQuery as Mock).mockReturnValue({
      data: [],
      isLoading: false,
      isError: false,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Create workgroup', () => {
    it('renders Create workgroup title', () => {
      render(<CreateOrEditWorkgroup initiativeId={initiativeId} />);

      expect(screen.getByText('Create workgroup')).toBeInTheDocument();
    });

    it('disable Manage workgroup users tab', () => {
      render(<CreateOrEditWorkgroup initiativeId={initiativeId} />);

      expect(screen.getByRole('button', { name: 'Manage workgroup users' })).toBeDisabled();
    });

    it('render Configuration', () => {
      render(<CreateOrEditWorkgroup initiativeId={initiativeId} />);

      expect(screen.getByTestId('configuration')).toBeInTheDocument();
    });
  });

  describe('Edit workgroup', () => {
    it('renders the Loader component when the workgroup data is being fetched', () => {
      (useGetWorkgroupQuery as Mock).mockReturnValue({ data: undefined, isFetching: true });

      render(<CreateOrEditWorkgroup initiativeId={initiativeId} workgroupId={workgroupId} />);

      expect(screen.getByTestId('loader')).toBeInTheDocument();
    });
    it('renders Edit workgroup title', () => {
      render(<CreateOrEditWorkgroup initiativeId={initiativeId} workgroupId={workgroupId} />);
      expect(screen.getByText('Edit workgroup')).toBeInTheDocument();
    });

    it('enable Manage workgroup users tab', () => {
      render(<CreateOrEditWorkgroup initiativeId={initiativeId} workgroupId={workgroupId} />);
      expect(screen.getByRole('button', { name: 'Manage workgroup users' })).not.toBeDisabled();
    });

    it('render Configuration', () => {
      render(<CreateOrEditWorkgroup initiativeId={initiativeId} workgroupId={workgroupId} />);
      expect(screen.getByTestId('configuration')).toBeInTheDocument();
    });

    it('render ManageUsers if workgroup data is available', async () => {
      (useGetWorkgroupQuery as Mock).mockReturnValue({
        data: { _id: workgroupId },
        isLoading: false,
        isError: false,
      });

      render(<CreateOrEditWorkgroup initiativeId={initiativeId} workgroupId={workgroupId} />);
      await userEvent.click(screen.getByRole('button', { name: 'Manage workgroup users' }));

      expect(screen.getByTestId('manage-users')).toBeInTheDocument();
    });

    it('should render ManageUsers when tab is set to Users', () => {
      (useGetWorkgroupQuery as Mock).mockReturnValue({
        data: { _id: workgroupId },
        isLoading: false,
        isError: false,
      });

      render(
        <CreateOrEditWorkgroup initiativeId={initiativeId} workgroupId={workgroupId} tab={ManageWorkgroupTab.Users} />,
      );

      expect(screen.getByTestId('manage-users')).toBeInTheDocument();
    });
  });
});
