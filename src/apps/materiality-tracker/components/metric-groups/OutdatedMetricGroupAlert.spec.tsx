import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { renderWithProviders } from '@fixtures/utils';
import { reduxFixtureStore } from '@fixtures/redux-store';
import { createMetricGroup } from '@fixtures/metric-group-fixtures';
import { OutdatedMetricGroupAlert } from './OutdatedMetricGroupAlert';
import { JobStatus } from '@g17eco/types/background-jobs';
import { MetricGroupSourceType } from '@g17eco/types/metricGroup';
import { AssessmentType } from '@g17eco/types/survey';

// Mock the hooks and API
vi.mock('@apps/materiality-tracker/api/materiality-assessment', async (importOriginal) => {
  const actual = await importOriginal();
  return {
    ...actual,
    useGenerateScoresQuery: vi.fn(),
  };
});

vi.mock('./hooks/useRegenerateMetricGroup', () => ({
  useRegenerateMetricGroup: vi.fn(),
}));

vi.mock('@hooks/useToggle', () => ({
  useToggle: vi.fn(),
}));

// Mock the BasicAlert component
vi.mock('@g17eco/molecules/alert', () => ({
  BasicAlert: ({ children, type, className }: any) => (
    <div data-testid='basic-alert' data-type={type} className={className}>
      {children}
    </div>
  ),
}));

// Mock reactstrap components
vi.mock('reactstrap', () => ({
  Button: ({ children, onClick, color, size, ...props }: any) => (
    <button onClick={onClick} data-color={color} data-size={size} data-testid='button' {...props}>
      {children}
    </button>
  ),
  Modal: ({ children, isOpen, toggle, backdrop }: any) =>
    isOpen ? (
      <div data-testid='modal' data-backdrop={backdrop} onClick={toggle}>
        {children}
      </div>
    ) : null,
  ModalHeader: ({ children, toggle }: any) => (
    <div data-testid='modal-header'>
      {children}
      <button onClick={toggle} data-testid='modal-close'>
        ×
      </button>
    </div>
  ),
  ModalBody: ({ children }: any) => <div data-testid='modal-body'>{children}</div>,
  ModalFooter: ({ children }: any) => <div data-testid='modal-footer'>{children}</div>,
}));

describe('OutdatedMetricGroupAlert', () => {
  const mockHandleRegenerate = vi.fn();
  const mockToggleRegenerateModal = vi.fn();
  const mockSetRegenerateModal = vi.fn();

  const baseMetricGroup = createMetricGroup({
    _id: 'metric-group-1',
    initiativeId: 'initiative-1',
    survey: { _id: 'survey-1', assessmentType: AssessmentType.DoubleMateriality, completedDate: '2024-01-01', effectiveDate: '2024-01-01' },
    source: {
      type: MetricGroupSourceType.Survey,
      surveyId: 'survey-1',
      jobId: 'job-1',
      topTopicsCount: 10,
      topicUtrs: [{ _id: 'utr-1' }],
    },
    updated: '2024-01-01T00:00:00.000Z',
    subgroups: [createMetricGroup({ _id: 'subgroup-1' })],
  });

  const createMockQueryResult = (overrides = {}) => ({
    data: undefined,
    isFetching: false,
    isLoading: false,
    isError: false,
    error: undefined,
    refetch: vi.fn(),
    ...overrides,
  });

  beforeEach(async () => {
    vi.clearAllMocks();

    // Get the mocked functions
    const { useGenerateScoresQuery } = await import('@apps/materiality-tracker/api/materiality-assessment');
    const { useRegenerateMetricGroup } = await import('./hooks/useRegenerateMetricGroup');
    const { useToggle } = await import('@hooks/useToggle');

    // Setup default mock implementations
    vi.mocked(useToggle).mockReturnValue([false, mockToggleRegenerateModal, mockSetRegenerateModal]);
    vi.mocked(useRegenerateMetricGroup).mockReturnValue({ handleRegenerate: mockHandleRegenerate });
    vi.mocked(useGenerateScoresQuery).mockReturnValue(createMockQueryResult());
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  const renderComponent = (metricGroup = baseMetricGroup) => {
    const store = reduxFixtureStore();
    return renderWithProviders(<OutdatedMetricGroupAlert metricGroup={metricGroup} />, { store });
  };

  describe('when loading score job', () => {
    it('should not render anything when loading', async () => {
      const { useGenerateScoresQuery } = await import('@apps/materiality-tracker/api/materiality-assessment');
      vi.mocked(useGenerateScoresQuery).mockReturnValue(createMockQueryResult({
        data: undefined,
        isFetching: true,
      }));

      const { container } = renderComponent();
      expect(container.firstChild).toBeNull();
    });
  });

  describe('when metric group is not outdated', () => {
    it('should not render when no source jobId exists', () => {
      const metricGroupWithoutJobId = createMetricGroup({
        ...baseMetricGroup,
        source: undefined,
      });

      const { container } = renderComponent(metricGroupWithoutJobId);
      expect(container.firstChild).toBeNull();
    });

    it('should not render when latest score job is not completed', async () => {
      const { useGenerateScoresQuery } = await import('@apps/materiality-tracker/api/materiality-assessment');
      vi.mocked(useGenerateScoresQuery).mockReturnValue(createMockQueryResult({
        data: {
          status: JobStatus.Processing,
          updatedAt: '2024-01-02T00:00:00.000Z',
        },
        isFetching: false,
      }));

      const { container } = renderComponent();
      expect(container.firstChild).toBeNull();
    });

    it('should not render when metric group is newer than latest score job', async () => {
      const { useGenerateScoresQuery } = await import('@apps/materiality-tracker/api/materiality-assessment');
      vi.mocked(useGenerateScoresQuery).mockReturnValue(createMockQueryResult({
        data: {
          status: JobStatus.Completed,
          updatedAt: '2023-12-31T00:00:00.000Z', // Older than metric group
        },
        isFetching: false,
      }));

      const { container } = renderComponent();
      expect(container.firstChild).toBeNull();
    });
  });

  describe('when metric group is outdated', () => {
    beforeEach(async () => {
      const { useGenerateScoresQuery } = await import('@apps/materiality-tracker/api/materiality-assessment');
      vi.mocked(useGenerateScoresQuery).mockReturnValue(createMockQueryResult({
        data: {
          status: JobStatus.Completed,
          updatedAt: '2024-01-02T00:00:00.000Z', // Newer than metric group
        },
        isFetching: false,
      }));
    });

    it('should render the alert with correct content', () => {
      renderComponent();

      expect(screen.getByTestId('basic-alert')).toBeInTheDocument();
      expect(screen.getByTestId('basic-alert')).toHaveAttribute('data-type', 'warning');
      expect(screen.getByText(/Topics for this module have changed/)).toBeInTheDocument();
      expect(screen.getByText(/but the module remain unchanged/)).toBeInTheDocument();
      expect(screen.getByText(/Would you like to regenerate this module/)).toBeInTheDocument();
    });

    it('should render the regenerate button', () => {
      renderComponent();

      const regenerateButton = screen.getByRole('button', { name: /Regenerate/ });
      expect(regenerateButton).toBeInTheDocument();
      expect(regenerateButton).toHaveAttribute('data-color', 'warning');
      expect(regenerateButton).toHaveAttribute('data-size', 'sm');
    });

    it('should open modal when regenerate button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const regenerateButton = screen.getByRole('button', { name: /Regenerate/ });
      await user.click(regenerateButton);

      expect(mockSetRegenerateModal).toHaveBeenCalledWith(true);
    });
  });

  describe('when metric group has no subgroups', () => {
    it('should render alert even when not outdated if no subgroups exist', async () => {
      const metricGroupWithoutSubgroups = createMetricGroup({
        ...baseMetricGroup,
        subgroups: undefined,
      });

      const { useGenerateScoresQuery } = await import('@apps/materiality-tracker/api/materiality-assessment');
      vi.mocked(useGenerateScoresQuery).mockReturnValue(createMockQueryResult({
        data: {
          status: JobStatus.Completed,
          updatedAt: '2023-12-31T00:00:00.000Z', // Older than metric group
        },
        isFetching: false,
      }));

      renderComponent(metricGroupWithoutSubgroups);
      expect(screen.getByTestId('basic-alert')).toBeInTheDocument();
    });
  });

  describe('RegenerateModal', () => {
    beforeEach(async () => {
      const { useGenerateScoresQuery } = await import('@apps/materiality-tracker/api/materiality-assessment');
      const { useToggle } = await import('@hooks/useToggle');

      vi.mocked(useGenerateScoresQuery).mockReturnValue(createMockQueryResult({
        data: {
          status: JobStatus.Completed,
          updatedAt: '2024-01-02T00:00:00.000Z',
        },
        isFetching: false,
      }));

      // Mock modal as open
      vi.mocked(useToggle).mockReturnValue([true, mockToggleRegenerateModal, mockSetRegenerateModal]);
    });

    it('should render modal when open', () => {
      renderComponent();

      expect(screen.getByTestId('modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal')).toHaveAttribute('data-backdrop', 'static');
    });

    it('should render modal header with correct title', () => {
      renderComponent();

      const modalHeader = screen.getByTestId('modal-header');
      expect(modalHeader).toBeInTheDocument();
      expect(modalHeader).toHaveTextContent('Regenerate module');
    });

    it('should render modal body with warning messages', () => {
      renderComponent();

      expect(screen.getByTestId('modal-body')).toBeInTheDocument();
      expect(screen.getByText(/Changes have been made to the underlining material topics/)).toBeInTheDocument();
      expect(screen.getByText(/Please note: Regenerating the module will overwrite/)).toBeInTheDocument();
    });

    it('should render modal footer with cancel and regenerate buttons', () => {
      renderComponent();

      expect(screen.getByTestId('modal-footer')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Regenerate module' })).toBeInTheDocument();
    });

    it('should close modal when cancel button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const cancelButton = screen.getByRole('button', { name: 'Cancel' });
      await user.click(cancelButton);

      expect(mockToggleRegenerateModal).toHaveBeenCalled();
    });

    it('should call handleRegenerate when regenerate button is clicked', async () => {
      const user = userEvent.setup();
      renderComponent();

      const regenerateButton = screen.getByRole('button', { name: 'Regenerate module' });
      await user.click(regenerateButton);

      expect(mockHandleRegenerate).toHaveBeenCalledWith(baseMetricGroup, 10);
    });

    it('should close modal when clicking outside (toggle)', async () => {
      const user = userEvent.setup();
      renderComponent();

      const modal = screen.getByTestId('modal');
      await user.click(modal);

      expect(mockToggleRegenerateModal).toHaveBeenCalled();
    });
  });

  describe('API integration', () => {
    it('should call useGenerateScoresQuery with correct parameters', async () => {
      const { useGenerateScoresQuery } = await import('@apps/materiality-tracker/api/materiality-assessment');
      renderComponent();

      expect(vi.mocked(useGenerateScoresQuery)).toHaveBeenCalledWith({
        initiativeId: 'initiative-1',
        surveyId: 'survey-1',
      });
    });

    it('should call useGenerateScoresQuery with skipToken when missing data', async () => {
      const { useGenerateScoresQuery } = await import('@apps/materiality-tracker/api/materiality-assessment');
      const metricGroupWithoutSurvey = createMetricGroup({
        ...baseMetricGroup,
        survey: undefined,
      });

      renderComponent(metricGroupWithoutSurvey);

      expect(vi.mocked(useGenerateScoresQuery)).toHaveBeenCalledWith(expect.any(Symbol)); // skipToken
    });

    it('should initialize useRegenerateMetricGroup with callback', async () => {
      const { useRegenerateMetricGroup } = await import('./hooks/useRegenerateMetricGroup');
      renderComponent();

      expect(vi.mocked(useRegenerateMetricGroup)).toHaveBeenCalledWith({
        callback: expect.any(Function),
      });

      // Test the callback
      const callback = vi.mocked(useRegenerateMetricGroup).mock.calls[0]?.[0]?.callback;
      if (callback) {
        callback();
        expect(mockSetRegenerateModal).toHaveBeenCalledWith(false);
      }
    });
  });
});
