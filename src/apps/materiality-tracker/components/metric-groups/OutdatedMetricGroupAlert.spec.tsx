import { vi, Mock } from 'vitest';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { OutdatedMetricGroupAlert } from './OutdatedMetricGroupAlert';
import { useGenerateScoresQuery } from '@apps/materiality-tracker/api/materiality-assessment';
import { useRegenerateMetricGroup } from './hooks/useRegenerateMetricGroup';
import { useToggle } from '@hooks/useToggle';
import { JobStatus } from '@g17eco/types/background-jobs';
import { MetricGroupSourceType } from '@g17eco/types/metricGroup';
import { AssessmentType } from '@g17eco/types/survey';
import { createMetricGroup } from '@fixtures/metric-group-fixtures';

vi.mock('@apps/materiality-tracker/api/materiality-assessment', () => ({
  useGenerateScoresQuery: vi.fn(),
}));

vi.mock('./hooks/useRegenerateMetricGroup', () => ({
  useRegenerateMetricGroup: vi.fn(),
}));

vi.mock('@hooks/useToggle', () => ({
  useToggle: vi.fn(),
}));

vi.mock('@g17eco/molecules/alert', () => ({
  BasicAlert: ({ children, type, className }: any) => (
    <div data-testid='basic-alert' data-type={type} className={className}>
      {children}
    </div>
  ),
}));

vi.mock('reactstrap', () => ({
  Button: ({ children, onClick, color, size, ...props }: any) => (
    <button onClick={onClick} data-color={color} data-size={size} data-testid='button' {...props}>
      {children}
    </button>
  ),
  Modal: ({ children, isOpen, toggle, backdrop }: any) =>
    isOpen ? (
      <div data-testid='modal' data-backdrop={backdrop}>
        {children}
      </div>
    ) : null,
  ModalHeader: ({ children, toggle }: any) => (
    <div data-testid='modal-header'>
      {children}
      <button onClick={toggle} data-testid='modal-close'>
        ×
      </button>
    </div>
  ),
  ModalBody: ({ children }: any) => <div data-testid='modal-body'>{children}</div>,
  ModalFooter: ({ children }: any) => <div data-testid='modal-footer'>{children}</div>,
}));

describe('OutdatedMetricGroupAlert component', () => {
  const mockHandleRegenerate = vi.fn();
  const mockToggleRegenerateModal = vi.fn();
  const mockSetRegenerateModal = vi.fn();

  const baseMetricGroup = createMetricGroup({
    _id: 'metric-group-1',
    initiativeId: 'initiative-1',
    survey: { _id: 'survey-1', assessmentType: AssessmentType.DoubleMateriality, completedDate: '2024-01-01', effectiveDate: '2024-01-01' },
    source: {
      type: MetricGroupSourceType.Survey,
      surveyId: 'survey-1',
      jobId: 'job-1',
      topTopicsCount: 10,
      topicUtrs: [{ _id: 'utr-1' }],
    },
    updated: '2024-01-01T00:00:00.000Z',
    subgroups: [createMetricGroup({ _id: 'subgroup-1' })],
  });

  beforeEach(() => {
    (useGenerateScoresQuery as Mock).mockReturnValue({
      data: undefined,
      isFetching: false,
      isError: false,
    });
    (useRegenerateMetricGroup as Mock).mockReturnValue({
      handleRegenerate: mockHandleRegenerate,
    });
    (useToggle as Mock).mockReturnValue([false, mockToggleRegenerateModal, mockSetRegenerateModal]);
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Loading state', () => {
    it('should not render anything when loading', () => {
      (useGenerateScoresQuery as Mock).mockReturnValue({
        data: undefined,
        isFetching: true,
      });

      const { container } = render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Non-outdated scenarios', () => {
    it('should not render when no source jobId exists', () => {
      const metricGroupWithoutJobId = createMetricGroup({
        ...baseMetricGroup,
        source: undefined,
      });

      const { container } = render(<OutdatedMetricGroupAlert metricGroup={metricGroupWithoutJobId} />);
      expect(container.firstChild).toBeNull();
    });

    it('should not render when latest score job is not completed', () => {
      (useGenerateScoresQuery as Mock).mockReturnValue({
        data: {
          status: JobStatus.Processing,
          updatedAt: '2024-01-02T00:00:00.000Z',
        },
        isFetching: false,
      });

      const { container } = render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);
      expect(container.firstChild).toBeNull();
    });

    it('should not render when metric group is newer than latest score job', () => {
      (useGenerateScoresQuery as Mock).mockReturnValue({
        data: {
          status: JobStatus.Completed,
          updatedAt: '2023-12-31T00:00:00.000Z', // Older than metric group
        },
        isFetching: false,
      });

      const { container } = render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);
      expect(container.firstChild).toBeNull();
    });
  });

  describe('Outdated metric group', () => {
    beforeEach(() => {
      (useGenerateScoresQuery as Mock).mockReturnValue({
        data: {
          status: JobStatus.Completed,
          updatedAt: '2024-01-02T00:00:00.000Z', // Newer than metric group
        },
        isFetching: false,
      });
    });

    it('renders the alert with correct content', () => {
      render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);

      expect(screen.getByTestId('basic-alert')).toBeInTheDocument();
      expect(screen.getByTestId('basic-alert')).toHaveAttribute('data-type', 'warning');
      expect(screen.getByText(/Topics for this module have changed/)).toBeInTheDocument();
      expect(screen.getByText(/but the module remain unchanged/)).toBeInTheDocument();
      expect(screen.getByText(/Would you like to regenerate this module/)).toBeInTheDocument();
    });

    it('renders the regenerate button', () => {
      render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);

      const regenerateButton = screen.getByRole('button', { name: /Regenerate/ });
      expect(regenerateButton).toBeInTheDocument();
      expect(regenerateButton).toHaveAttribute('data-color', 'warning');
      expect(regenerateButton).toHaveAttribute('data-size', 'sm');
    });

    it('opens modal when regenerate button is clicked', async () => {
      const user = userEvent.setup();
      render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);

      const regenerateButton = screen.getByRole('button', { name: /Regenerate/ });
      await user.click(regenerateButton);

      expect(mockSetRegenerateModal).toHaveBeenCalledWith(true);
    });
  });

  describe('Metric group with no subgroups', () => {
    it('renders alert even when not outdated if no subgroups exist', () => {
      const metricGroupWithoutSubgroups = createMetricGroup({
        ...baseMetricGroup,
        subgroups: undefined,
      });

      (useGenerateScoresQuery as Mock).mockReturnValue({
        data: {
          status: JobStatus.Completed,
          updatedAt: '2023-12-31T00:00:00.000Z', // Older than metric group
        },
        isFetching: false,
      });

      render(<OutdatedMetricGroupAlert metricGroup={metricGroupWithoutSubgroups} />);
      expect(screen.getByTestId('basic-alert')).toBeInTheDocument();
    });
  });

  describe('RegenerateModal', () => {
    beforeEach(() => {
      (useGenerateScoresQuery as Mock).mockReturnValue({
        data: {
          status: JobStatus.Completed,
          updatedAt: '2024-01-02T00:00:00.000Z',
        },
        isFetching: false,
      });

      // Mock modal as open
      (useToggle as Mock).mockReturnValue([true, mockToggleRegenerateModal, mockSetRegenerateModal]);
    });

    it('renders modal when open', () => {
      render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);

      expect(screen.getByTestId('modal')).toBeInTheDocument();
      expect(screen.getByTestId('modal')).toHaveAttribute('data-backdrop', 'static');
    });

    it('renders modal header with correct title', () => {
      render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);

      const modalHeader = screen.getByTestId('modal-header');
      expect(modalHeader).toBeInTheDocument();
      expect(modalHeader).toHaveTextContent('Regenerate module');
    });

    it('renders modal body with warning messages', () => {
      render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);

      expect(screen.getByTestId('modal-body')).toBeInTheDocument();
      expect(screen.getByText(/Changes have been made to the underlining material topics/)).toBeInTheDocument();
      expect(screen.getByText(/Please note: Regenerating the module will overwrite/)).toBeInTheDocument();
    });

    it('renders modal footer with cancel and regenerate buttons', () => {
      render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);

      expect(screen.getByTestId('modal-footer')).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Regenerate module' })).toBeInTheDocument();
    });

    it('closes modal when cancel button is clicked', async () => {
      const user = userEvent.setup();
      render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);

      const cancelButton = screen.getByRole('button', { name: 'Cancel' });
      await user.click(cancelButton);

      expect(mockToggleRegenerateModal).toHaveBeenCalled();
    });

    it('calls handleRegenerate when regenerate button is clicked', async () => {
      const user = userEvent.setup();
      render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);

      const regenerateButton = screen.getByRole('button', { name: 'Regenerate module' });
      await user.click(regenerateButton);

      expect(mockHandleRegenerate).toHaveBeenCalledWith(baseMetricGroup, 10);
    });
  });

  describe('API integration', () => {
    it('calls useGenerateScoresQuery with correct parameters', () => {
      render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);

      expect(useGenerateScoresQuery).toHaveBeenCalledWith({
        initiativeId: 'initiative-1',
        surveyId: 'survey-1',
      });
    });

    it('calls useGenerateScoresQuery with skipToken when missing data', () => {
      const metricGroupWithoutSurvey = createMetricGroup({
        ...baseMetricGroup,
        survey: undefined,
      });

      render(<OutdatedMetricGroupAlert metricGroup={metricGroupWithoutSurvey} />);

      expect(useGenerateScoresQuery).toHaveBeenCalledWith(expect.any(Symbol)); // skipToken
    });

    it('initializes useRegenerateMetricGroup with callback', () => {
      render(<OutdatedMetricGroupAlert metricGroup={baseMetricGroup} />);

      expect(useRegenerateMetricGroup).toHaveBeenCalledWith({
        callback: expect.any(Function),
      });

      // Test the callback
      const callback = (useRegenerateMetricGroup as Mock).mock.calls[0]?.[0]?.callback;
      if (callback) {
        callback();
        expect(mockSetRegenerateModal).toHaveBeenCalledWith(false);
      }
    });
  });
});
