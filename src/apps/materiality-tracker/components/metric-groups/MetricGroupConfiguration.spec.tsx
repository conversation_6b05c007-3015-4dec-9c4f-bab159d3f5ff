import { vi, Mock } from 'vitest';
import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { MetricGroupConfiguration } from './MetricGroupConfiguration';
import { useGetAssessmentSizeQuery, MaterialityAssessmentScope } from '@apps/materiality-tracker/api/materiality-assessment';
import { useRegenerateMetricGroup } from './hooks/useRegenerateMetricGroup';
import { createMetricGroup } from '@fixtures/metric-group-fixtures';
import { MetricGroupSourceType } from '@g17eco/types/metricGroup';
import { AssessmentType } from '@g17eco/types/survey';

vi.mock('@apps/materiality-tracker/api/materiality-assessment', async (importOriginal) => {
  const actual = await importOriginal<typeof import('@apps/materiality-tracker/api/materiality-assessment')>();
  return {
    ...actual,
    useGetAssessmentSizeQuery: vi.fn(),
  };
});

vi.mock('./hooks/useRegenerateMetricGroup', () => ({
  useRegenerateMetricGroup: vi.fn(),
}));

describe('MetricGroupConfiguration component', () => {
  const mockHandleRegenerate = vi.fn();

  const baseMetricGroup = createMetricGroup({
    _id: 'metric-group-1',
    initiativeId: 'initiative-1',
    survey: {
      _id: 'survey-1',
      assessmentType: AssessmentType.DoubleMateriality,
      completedDate: '2024-01-01',
      effectiveDate: '2024-01-01',
    },
    source: {
      type: MetricGroupSourceType.Survey,
      surveyId: 'survey-1',
      jobId: 'job-1',
      topTopicsCount: 15,
      topicUtrs: [{ _id: 'utr-1' }],
    },
  });

  beforeEach(() => {
    (useGetAssessmentSizeQuery as Mock).mockReturnValue({
      data: { sizeScope: MaterialityAssessmentScope.SME },
      isFetching: false,
    });
    (useRegenerateMetricGroup as Mock).mockReturnValue({
      handleRegenerate: mockHandleRegenerate,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Loading state', () => {
    it('renders the Loader component when assessment size is being fetched', () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: undefined,
        isFetching: true,
      });

      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByTestId('loader')).toBeInTheDocument();
    });
  });

  describe('Content rendering', () => {
    it('renders Module scope title', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByText('Module scope')).toBeInTheDocument();
    });

    it('renders description text', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByText(/Which segment of topics would you like to use to generate metrics for this custom module/)).toBeInTheDocument();
    });

    it('renders the select component', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    it('renders Cancel and Update buttons', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Update assigned metrics' })).toBeInTheDocument();
    });
  });

  describe('Select options for different scopes', () => {
    it('generates correct options for SME scope', async () => {
      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const combobox = screen.getByRole('combobox');
      await user.click(combobox);

      // Find the dropdown menu by its class and check options within it
      const menu = document.querySelector('.css-1k4rsfp-menu');
      expect(menu).toBeInTheDocument();

      // SME scope has max 20 topics, so options should be 5, 10, 15, 20
      expect(within(menu as HTMLElement).getByText('Top 5 topics')).toBeInTheDocument();
      expect(within(menu as HTMLElement).getByText('Top 10 topics')).toBeInTheDocument();
      expect(within(menu as HTMLElement).getByText('Top 15 topics')).toBeInTheDocument();
      expect(within(menu as HTMLElement).getByText('Top 20 topics')).toBeInTheDocument();
    });

    it('generates correct options for Micro scope', async () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: { sizeScope: MaterialityAssessmentScope.Micro },
        isFetching: false,
      });

      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const combobox = screen.getByRole('combobox');
      await user.click(combobox);

      // Find the dropdown menu by its class and check options within it
      const menu = document.querySelector('.css-1k4rsfp-menu');
      expect(menu).toBeInTheDocument();

      // Micro scope has max 15 topics, so options should be 5, 10, 15
      expect(within(menu as HTMLElement).getByText('Top 5 topics')).toBeInTheDocument();
      expect(within(menu as HTMLElement).getByText('Top 10 topics')).toBeInTheDocument();
      expect(within(menu as HTMLElement).getByText('Top 15 topics')).toBeInTheDocument();
      expect(within(menu as HTMLElement).queryByText('Top 20 topics')).not.toBeInTheDocument();
    });

    it('generates correct options for Large scope', async () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: { sizeScope: MaterialityAssessmentScope.Large },
        isFetching: false,
      });

      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const combobox = screen.getByRole('combobox');
      await user.click(combobox);

      // Large scope has max 25 topics, so should include up to 25
      expect(screen.getByText('Top 25 topics')).toBeInTheDocument();
    });
  });

  describe('Button states', () => {
    it('disables Update button when no changes are made', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByRole('button', { name: 'Update assigned metrics' })).toBeDisabled();
    });

    it('enables Update button when selection changes', async () => {
      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const combobox = screen.getByRole('combobox');
      await user.click(combobox);

      const option = screen.getByText('Top 10 topics');
      await user.click(option);

      expect(screen.getByRole('button', { name: 'Update assigned metrics' })).not.toBeDisabled();
    });
  });

  describe('User interactions', () => {
    it('calls handleRegenerate when Update button is clicked', async () => {
      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      // Change selection to enable the button
      const combobox = screen.getByRole('combobox');
      await user.click(combobox);

      const option = screen.getByText('Top 10 topics');
      await user.click(option);

      const updateButton = screen.getByRole('button', { name: 'Update assigned metrics' });
      await user.click(updateButton);

      expect(mockHandleRegenerate).toHaveBeenCalledWith(baseMetricGroup, 10);
    });
  });

  describe('API integration', () => {
    it('calls useGetAssessmentSizeQuery with correct parameters', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(useGetAssessmentSizeQuery).toHaveBeenCalledWith({
        initiativeId: 'initiative-1',
        assessmentId: 'survey-1',
      });
    });

    it('calls useGetAssessmentSizeQuery with skipToken when missing survey data', () => {
      const metricGroupWithoutSurvey = createMetricGroup({
        ...baseMetricGroup,
        survey: undefined,
      });

      render(<MetricGroupConfiguration metricGroup={metricGroupWithoutSurvey} />);

      expect(useGetAssessmentSizeQuery).toHaveBeenCalledWith(expect.any(Symbol));
    });

    it('initializes useRegenerateMetricGroup hook', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(useRegenerateMetricGroup).toHaveBeenCalled();
    });
  });

  describe('Edge cases', () => {
    it('handles undefined scope gracefully', () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: { sizeScope: undefined },
        isFetching: false,
      });

      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });

    it('handles missing assessment data', () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: undefined,
        isFetching: false,
      });

      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByRole('combobox')).toBeInTheDocument();
    });
  });
});
