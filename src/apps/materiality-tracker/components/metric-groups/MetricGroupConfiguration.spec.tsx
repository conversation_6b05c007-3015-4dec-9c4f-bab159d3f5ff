import { vi, <PERSON>ck } from 'vitest';
import { render, screen } from '@testing-library/react';
import { MetricGroupConfiguration } from './MetricGroupConfiguration';
import { useGetAssessmentSizeQuery, MaterialityAssessmentScope } from '@apps/materiality-tracker/api/materiality-assessment';
import { useRegenerateMetricGroup } from './hooks/useRegenerateMetricGroup';
import userEvent from '@testing-library/user-event';
import { createMetricGroup } from '@fixtures/metric-group-fixtures';
import { MetricGroupSourceType } from '@g17eco/types/metricGroup';
import { AssessmentType } from '@g17eco/types/survey';

vi.mock('@apps/materiality-tracker/api/materiality-assessment', () => ({
  useGetAssessmentSizeQuery: vi.fn(),
}));

vi.mock('./hooks/useRegenerateMetricGroup', () => ({
  useRegenerateMetricGroup: vi.fn(),
}));

vi.mock('@g17eco/atoms/loader', () => ({
  BlockingLoader: () => <div data-testid='blocking-loader'>Loading...</div>,
}));

vi.mock('@g17eco/molecules/select/SelectFactory', () => ({
  SelectFactory: ({ options, value, onChange, isSearchable }: any) => (
    <select
      data-testid='select-factory'
      data-searchable={isSearchable}
      value={value?.value || ''}
      onChange={(e) => {
        const selectedOption = options.find((opt: any) => opt.value === Number(e.target.value));
        onChange(selectedOption);
      }}
    >
      <option value=''>Select...</option>
      {options.map((option: any) => (
        <option key={option.value} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  ),
  SelectTypes: {
    SingleSelect: 'single-select',
  },
}));

describe('MetricGroupConfiguration component', () => {
  const mockHandleRegenerate = vi.fn();

  const baseMetricGroup = createMetricGroup({
    _id: 'metric-group-1',
    initiativeId: 'initiative-1',
    survey: {
      _id: 'survey-1',
      assessmentType: AssessmentType.DoubleMateriality,
      completedDate: '2024-01-01',
      effectiveDate: '2024-01-01',
    },
    source: {
      type: MetricGroupSourceType.Survey,
      surveyId: 'survey-1',
      jobId: 'job-1',
      topTopicsCount: 15,
      topicUtrs: [{ _id: 'utr-1' }],
    },
  });

  beforeEach(() => {
    (useGetAssessmentSizeQuery as Mock).mockReturnValue({
      data: { sizeScope: MaterialityAssessmentScope.SME },
      isFetching: false,
    });
    (useRegenerateMetricGroup as Mock).mockReturnValue({
      handleRegenerate: mockHandleRegenerate,
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Loading state', () => {
    it('renders blocking loader when assessment size is loading', () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: undefined,
        isFetching: true,
      });

      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByTestId('blocking-loader')).toBeInTheDocument();
    });

    it('does not render blocking loader when not loading', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.queryByTestId('blocking-loader')).not.toBeInTheDocument();
    });
  });

  describe('Content rendering', () => {
    it('renders the module scope title', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByText('Module scope')).toBeInTheDocument();
    });

    it('renders the description text', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByText(/Which segment of topics would you like to use to generate metrics for this custom module/)).toBeInTheDocument();
    });

    it('renders the select component', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const select = screen.getByTestId('select-factory');
      expect(select).toBeInTheDocument();
      expect(select).toHaveAttribute('data-searchable', 'false');
    });

    it('renders cancel and update buttons', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(screen.getByRole('button', { name: 'Cancel' })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: 'Update assigned metrics' })).toBeInTheDocument();
    });
  });

  describe('Select options generation', () => {
    it('generates correct options for SME scope', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const select = screen.getByTestId('select-factory');
      
      // SME scope has max 20 topics, so options should be 5, 10, 15, 20
      expect(screen.getByRole('option', { name: 'Top 5 topics' })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: 'Top 10 topics' })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: 'Top 15 topics' })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: 'Top 20 topics' })).toBeInTheDocument();
    });

    it('generates correct options for Micro scope', () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: { sizeScope: MaterialityAssessmentScope.Micro },
        isFetching: false,
      });

      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      // Micro scope has max 15 topics, so options should be 5, 10, 15
      expect(screen.getByRole('option', { name: 'Top 5 topics' })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: 'Top 10 topics' })).toBeInTheDocument();
      expect(screen.getByRole('option', { name: 'Top 15 topics' })).toBeInTheDocument();
      expect(screen.queryByRole('option', { name: 'Top 20 topics' })).not.toBeInTheDocument();
    });

    it('generates correct options for Large scope', () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: { sizeScope: MaterialityAssessmentScope.Large },
        isFetching: false,
      });

      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      // Large scope has max 25 topics, so should include up to 25
      expect(screen.getByRole('option', { name: 'Top 25 topics' })).toBeInTheDocument();
    });
  });

  describe('Default value selection', () => {
    it('selects the current topTopicsCount from metric group source', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const select = screen.getByTestId('select-factory') as HTMLSelectElement;
      expect(select.value).toBe('15'); // From baseMetricGroup.source.topTopicsCount
    });

    it('selects the max value when no source topTopicsCount exists', () => {
      const metricGroupWithoutSource = createMetricGroup({
        ...baseMetricGroup,
        source: undefined,
      });

      render(<MetricGroupConfiguration metricGroup={metricGroupWithoutSource} />);

      const select = screen.getByTestId('select-factory') as HTMLSelectElement;
      expect(select.value).toBe('20'); // Max for SME scope
    });
  });

  describe('Button states', () => {
    it('disables update button when no changes are made', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const updateButton = screen.getByRole('button', { name: 'Update assigned metrics' });
      expect(updateButton).toBeDisabled();
    });

    it('enables update button when selection changes', async () => {
      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const select = screen.getByTestId('select-factory');
      await user.selectOptions(select, '10');

      const updateButton = screen.getByRole('button', { name: 'Update assigned metrics' });
      expect(updateButton).not.toBeDisabled();
    });
  });

  describe('User interactions', () => {
    it('updates selection when user changes select value', async () => {
      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const select = screen.getByTestId('select-factory') as HTMLSelectElement;
      await user.selectOptions(select, '10');

      expect(select.value).toBe('10');
    });

    it('calls handleRegenerate when update button is clicked', async () => {
      const user = userEvent.setup();
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      // Change selection to enable the button
      const select = screen.getByTestId('select-factory');
      await user.selectOptions(select, '10');

      const updateButton = screen.getByRole('button', { name: 'Update assigned metrics' });
      await user.click(updateButton);

      expect(mockHandleRegenerate).toHaveBeenCalledWith(baseMetricGroup, 10);
    });
  });

  describe('API integration', () => {
    it('calls useGetAssessmentSizeQuery with correct parameters', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(useGetAssessmentSizeQuery).toHaveBeenCalledWith({
        initiativeId: 'initiative-1',
        assessmentId: 'survey-1',
      });
    });

    it('calls useGetAssessmentSizeQuery with skipToken when missing data', () => {
      const metricGroupWithoutSurvey = createMetricGroup({
        ...baseMetricGroup,
        survey: undefined,
      });

      render(<MetricGroupConfiguration metricGroup={metricGroupWithoutSurvey} />);

      expect(useGetAssessmentSizeQuery).toHaveBeenCalledWith(expect.any(Symbol)); // skipToken
    });

    it('initializes useRegenerateMetricGroup hook', () => {
      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      expect(useRegenerateMetricGroup).toHaveBeenCalled();
    });
  });

  describe('Edge cases', () => {
    it('handles undefined scope gracefully', () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: { sizeScope: undefined },
        isFetching: false,
      });

      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      // Should only show the default "Select..." option
      const select = screen.getByTestId('select-factory');
      expect(select).toBeInTheDocument();
      expect(screen.queryByRole('option', { name: 'Top 5 topics' })).not.toBeInTheDocument();
    });

    it('handles missing assessment data', () => {
      (useGetAssessmentSizeQuery as Mock).mockReturnValue({
        data: undefined,
        isFetching: false,
      });

      render(<MetricGroupConfiguration metricGroup={baseMetricGroup} />);

      const select = screen.getByTestId('select-factory');
      expect(select).toBeInTheDocument();
    });
  });
});
