import { SimpleTooltip } from '@g17eco/molecules/simple-tooltip';
import { useLayoutEffect, useRef, useState } from 'react';

const MARGIN = 4;

export const BadgesTruncate = ({ badges }: { badges: JSX.Element[] }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [visibleCount, setVisibleCount] = useState(badges.length);

  useLayoutEffect(() => {
    const container = containerRef.current;
    if (!container) {
      return;
    }

    const children = Array.from(container.children);
    let totalWidth = 0;
    let maxVisible = badges.length;

    for (let i = 0; i < children.length; i++) {
      const child = children[i] as HTMLElement;
      totalWidth += child.offsetWidth + MARGIN;
      if (totalWidth > container.offsetWidth) {
        maxVisible = i;
        break;
      }
    }

    setVisibleCount(maxVisible);
  }, [badges]);

  const visibleBadges = badges.slice(0, visibleCount);
  const hiddenBadges = badges.slice(visibleCount);

  return (
    <div className='badge-container' ref={containerRef}>
      {visibleBadges}
      {hiddenBadges.length > 0 && (
        <SimpleTooltip text={<>{hiddenBadges}</>}>
          <div className='badge background-ThemeNeutralsExtradark cursor-pointer'>...</div>
        </SimpleTooltip>
      )}
    </div>
  );
};
